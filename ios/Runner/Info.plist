<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Dasso Reader</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleSpokenName</key>
	<string>Dasso Reader</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.education</string>
	<key>LSMinimumSystemVersion</key>
	<string>12.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>zh_CN</string>
		<string>zh_TW</string>
		<string>zh</string>
		<string>tr</string>
	</array>
	<key>CFBundleName</key>
	<string>dasso_reader</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSPrivacyAccessedAPITypes</key>
	<array>
		<dict>
			<key>NSPrivacyAccessedAPIType</key>
			<string>NSPrivacyAccessedAPICategoryFileTimestamp</string>
			<key>NSPrivacyAccessedAPITypeReasons</key>
			<array>
				<string>C617.1</string>
			</array>
		</dict>
		<dict>
			<key>NSPrivacyAccessedAPIType</key>
			<string>NSPrivacyAccessedAPICategorySystemBootTime</string>
			<key>NSPrivacyAccessedAPITypeReasons</key>
			<array>
				<string>35F9.1</string>
			</array>
		</dict>
		<dict>
			<key>NSPrivacyAccessedAPIType</key>
			<string>NSPrivacyAccessedAPICategoryDiskSpace</string>
			<key>NSPrivacyAccessedAPITypeReasons</key>
			<array>
				<string>E174.1</string>
			</array>
		</dict>
	</array>
	<key>NSPrivacyCollectedDataTypes</key>
	<array>
		<dict>
			<key>NSPrivacyCollectedDataType</key>
			<string>NSPrivacyCollectedDataTypeOtherUsageData</string>
			<key>NSPrivacyCollectedDataTypeLinked</key>
			<false/>
			<key>NSPrivacyCollectedDataTypeTracking</key>
			<false/>
			<key>NSPrivacyCollectedDataTypePurposes</key>
			<array>
				<string>NSPrivacyCollectedDataTypePurposeAppFunctionality</string>
			</array>
		</dict>
	</array>
	<key>NSPrivacyTrackingDomains</key>
	<array/>
	<key>NSPrivacyTracking</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Dasso Reader needs access to save book covers and reading screenshots to your photo library for personal use and sharing.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Dasso Reader needs access to save book covers and reading screenshots to your photo library for personal use and sharing.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>This app does not track users across apps or websites. All data processing is done locally on your device for Chinese language learning features.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Location access is not required for Dasso Reader functionality.</string>
	<key>NSCameraUsageDescription</key>
	<string>Camera access is not required for Dasso Reader functionality.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Microphone access is not required for Dasso Reader functionality.</string>
	<key>NSContactsUsageDescription</key>
	<string>Contact access is not required for Dasso Reader functionality.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Calendar access is not required for Dasso Reader functionality.</string>
	<key>NSRemindersUsageDescription</key>
	<string>Reminders access is not required for Dasso Reader functionality.</string>
	<key>NSMotionUsageDescription</key>
	<string>Motion data is not required for Dasso Reader functionality.</string>
	<key>NSHealthShareUsageDescription</key>
	<string>Health data access is not required for Dasso Reader functionality.</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>Health data access is not required for Dasso Reader functionality.</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Bluetooth access is not required for Dasso Reader functionality.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Bluetooth access is not required for Dasso Reader functionality.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Speech recognition is not required for Dasso Reader functionality.</string>
	<key>NSAppleMusicUsageDescription</key>
	<string>Apple Music access is not required for Dasso Reader functionality.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>background-processing</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
				<string>public.composite-content</string>
			</array>
			<key>UTTypeDescription</key>
			<string>EPUB Document</string>
			<key>UTTypeIdentifier</key>
			<string>org.idpf.epub-container</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>epub</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>application/epub+zip</string>
				</array>
			</dict>
		</dict>
	</array>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>epub</string>
			</array>
			<key>CFBundleTypeIconFiles</key>
			<array/>
			<key>CFBundleTypeName</key>
			<string>EPUB Document</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.idpf.epub-container</string>
			</array>
		</dict>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>AppGroupId</key>
	<string>$(CUSTOM_GROUP_ID)</string>
	<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>ShareMedia-$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				</array>
			</dict>
		</array>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsLocalNetworking</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.0</string>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
</dict>
</plist>
