# DassoShu Reader - iOS Production Optimization
# Professional Chinese language learning e-book reader

# Target iOS 12.0+ for maximum compatibility while maintaining modern features
platform :ios, '12.0'

# Performance optimizations
ENV['COCOAPODS_DISABLE_STATS'] = 'true'
ENV['COCOAPODS_PARALLEL_CODE_SIGN'] = 'true'
ENV['COCOAPODS_DISABLE_DETERMINISTIC_UUIDS'] = 'true'

# Build performance improvements
install! 'cocoapods', :deterministic_uuids => false, :integrate_targets => false

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  target 'RunnerTests' do
    inherit! :search_paths
  end
end

# Set Swift version for all targets
post_integrate do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      if config.build_settings['PRODUCT_NAME'] == 'receive_sharing_intent'
        config.build_settings['SWIFT_VERSION'] = '5.0'
      end
    end
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)

    # Production build optimizations
    target.build_configurations.each do |config|
      # Enable bitcode for App Store optimization (if supported)
      config.build_settings['ENABLE_BITCODE'] = 'NO'  # Disabled for Flutter compatibility

      # Optimize for size and performance
      config.build_settings['GCC_OPTIMIZATION_LEVEL'] = 's'  # Optimize for size
      config.build_settings['SWIFT_OPTIMIZATION_LEVEL'] = '-O'  # Optimize Swift code

      # Enable dead code stripping
      config.build_settings['DEAD_CODE_STRIPPING'] = 'YES'
      config.build_settings['STRIP_INSTALLED_PRODUCT'] = 'YES'

      # Deployment target consistency
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'

      # Architecture optimizations
      config.build_settings['ONLY_ACTIVE_ARCH'] = 'NO'
      config.build_settings['VALID_ARCHS'] = 'arm64'
      config.build_settings['ARCHS'] = 'arm64'

      # Build performance improvements
      config.build_settings['COMPILER_INDEX_STORE_ENABLE'] = 'NO'
      config.build_settings['ENABLE_PREVIEWS'] = 'NO'

      # Security and privacy optimizations
      config.build_settings['ENABLE_HARDENED_RUNTIME'] = 'YES'
      config.build_settings['PRODUCT_BUNDLE_IDENTIFIER'] = 'io.github.dassodev.dassoReader'

      # Chinese language support optimizations
      config.build_settings['LOCALIZATION_PREFERS_STRING_CATALOGS'] = 'YES'

      # Memory and performance optimizations
      config.build_settings['GCC_GENERATE_DEBUGGING_SYMBOLS'] = config.name == 'Debug' ? 'YES' : 'NO'
      config.build_settings['DEBUG_INFORMATION_FORMAT'] = config.name == 'Debug' ? 'dwarf' : 'dwarf-with-dsym'

      # App Store compliance
      config.build_settings['SKIP_INSTALL'] = 'NO'
      config.build_settings['COPY_PHASE_STRIP'] = config.name == 'Release' ? 'YES' : 'NO'

      # Disable unnecessary warnings for third-party pods
      config.build_settings['GCC_WARN_INHIBIT_ALL_WARNINGS'] = 'YES'
      config.build_settings['CLANG_WARN_DOCUMENTATION_COMMENTS'] = 'NO'

      # Set Swift version for all pods
      if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.bundle"
        target.build_configurations.each do |config|
          config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
        end
      end
    end
  end

  # Additional project-level optimizations
  installer.pods_project.build_configurations.each do |config|
    config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
    config.build_settings['ENABLE_STRICT_OBJC_MSGSEND'] = 'YES'
    config.build_settings['GCC_NO_COMMON_BLOCKS'] = 'YES'
    config.build_settings['CLANG_ENABLE_OBJC_WEAK'] = 'YES'
  end
end
